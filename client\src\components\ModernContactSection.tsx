import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { 
  Mail, 
  Phone, 
  MapPin, 
  Send, 
  Github, 
  Linkedin, 
  MessageCircle,
  Zap,
  User,
  FileText
} from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

const contactSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
});

type ContactFormData = z.infer<typeof contactSchema>;

export default function ModernContactSection() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      name: '',
      email: '',
      subject: '',
      message: '',
    },
  });

  const contactMutation = useMutation({
    mutationFn: (data: ContactFormData) => 
      fetch('/api/contact', {
        method: 'POST',
        body: JSON.stringify(data),
        headers: { 'Content-Type': 'application/json' },
      }).then(res => res.json()),
    onSuccess: () => {
      toast({
        title: "Message sent successfully!",
        description: "Thank you for reaching out. I'll get back to you soon.",
      });
      form.reset();
      queryClient.invalidateQueries({ queryKey: ['/api/contacts'] });
    },
    onError: () => {
      toast({
        title: "Failed to send message",
        description: "Please try again or contact me directly via email.",
        variant: "destructive",
      });
    },
    onSettled: () => {
      setIsSubmitting(false);
    },
  });

  const onSubmit = (data: ContactFormData) => {
    setIsSubmitting(true);
    contactMutation.mutate(data);
  };

  const contactInfo = [
    {
      icon: Mail,
      label: "Email",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>",
      color: "text-[hsl(191,100%,50%)]"
    },
    {
      icon: Phone,
      label: "Phone",
      value: "+91-9838351173",
      href: "tel:+919838351173",
      color: "text-[hsl(261,100%,67%)]"
    },
    {
      icon: MapPin,
      label: "Location",
      value: "Lucknow, Uttar Pradesh",
      href: "#",
      color: "text-[hsl(213,100%,50%)]"
    }
  ];

  const socialLinks = [
    {
      icon: Github,
      label: "GitHub",
      href: "#",
      color: "hover:text-[hsl(191,100%,50%)]"
    },
    {
      icon: Linkedin,
      label: "LinkedIn",
      href: "#",
      color: "hover:text-[hsl(261,100%,67%)]"
    },
    {
      icon: Mail,
      label: "Email",
      href: "mailto:<EMAIL>",
      color: "hover:text-[hsl(213,100%,50%)]"
    }
  ];

  return (
    <section id="contact" className="py-12 sm:py-20 relative">
      <div className="container mx-auto px-4 sm:px-6">
        {/* Section Header */}
        <div className="text-center mb-8 sm:mb-16">
          <h2 className="font-orbitron text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black mb-4 sm:mb-6 tracking-wider">
            <span className="bg-gradient-to-r from-[#FF6B9D] via-[#8B5FFF] to-[#00D9FF] bg-clip-text text-transparent">
              GET
            </span>
            <span className="text-white/90 ml-2">_IN_TOUCH</span>
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-gray-300 max-w-2xl mx-auto">
            Ready to collaborate on your next project? Let's discuss how we can build something amazing together.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            {/* Main Contact Card */}
            <div className="glass-effect p-4 sm:p-6 lg:p-8 rounded-2xl border border-[hsl(191,100%,50%)]/30">
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-r from-[hsl(191,100%,50%)] to-[hsl(261,100%,67%)] rounded-2xl flex items-center justify-center">
                    <MessageCircle className="text-white" size={28} />
                  </div>
                  <div>
                    <h3 className="font-orbitron text-lg sm:text-xl lg:text-2xl font-bold text-[hsl(191,100%,50%)] mb-2">
                      LET'S CONNECT
                    </h3>
                    <p className="text-gray-300">
                      Always excited to discuss new opportunities and innovative projects.
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  {contactInfo.map((info) => {
                    const IconComponent = info.icon;
                    return (
                      <a
                        key={info.label}
                        href={info.href}
                        className="flex items-center space-x-4 p-4 cyber-card rounded-xl border border-gray-700/30 hover:border-[hsl(191,100%,50%)]/50 transition-all group"
                      >
                        <div className={`w-12 h-12 rounded-lg bg-black/50 border ${info.color.replace('text-', 'border-')}/30 flex items-center justify-center group-hover:scale-110 transition-transform`}>
                          <IconComponent className={info.color} size={20} />
                        </div>
                        <div>
                          <div className="font-fira text-sm text-gray-400 uppercase tracking-wider">
                            {info.label}
                          </div>
                          <div className="font-medium text-gray-200">
                            {info.value}
                          </div>
                        </div>
                      </a>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Social Links */}
            <div className="cyber-card p-6 rounded-xl border border-[hsl(261,100%,67%)]/30">
              <h4 className="font-orbitron text-lg font-bold text-[hsl(261,100%,67%)] mb-4 text-center">
                CONNECT ON SOCIAL
              </h4>
              <div className="flex justify-center space-x-4">
                {socialLinks.map((social) => {
                  const IconComponent = social.icon;
                  return (
                    <a
                      key={social.label}
                      href={social.href}
                      className={`w-14 h-14 glass-effect rounded-full border border-gray-600/30 flex items-center justify-center text-gray-400 ${social.color} transition-all hover:scale-110 hover:border-[hsl(261,100%,67%)]/50 group`}
                      aria-label={social.label}
                    >
                      <IconComponent className="group-hover:animate-pulse" size={24} />
                    </a>
                  );
                })}
              </div>
            </div>

            {/* Availability Status */}
            <div className="cyber-card p-6 rounded-xl border border-[hsl(213,100%,50%)]/30 text-center">
              <div className="flex items-center justify-center space-x-3 mb-3">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span className="font-orbitron text-lg font-bold text-[hsl(213,100%,50%)]">
                  AVAILABLE FOR WORK
                </span>
              </div>
              <p className="text-sm text-gray-400 font-fira">
                Open to full-time opportunities and exciting projects
              </p>
            </div>
          </div>

          {/* Contact Form */}
          <div className="glass-effect p-4 sm:p-6 lg:p-8 rounded-2xl border border-[hsl(191,100%,50%)]/30">
            <div className="mb-6">
              <div className="flex items-center space-x-3 mb-4">
                <Zap className="text-[hsl(191,100%,50%)]" size={24} />
                <h3 className="font-orbitron text-2xl font-bold text-[hsl(191,100%,50%)]">
                  SEND MESSAGE
                </h3>
              </div>
              <p className="text-gray-300">
                Drop me a line and I'll get back to you as soon as possible.
              </p>
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-fira text-sm font-bold text-gray-300 uppercase tracking-wider flex items-center space-x-2">
                          <User size={16} />
                          <span>Name</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Your full name"
                            className="bg-black/50 border-gray-600 focus:border-[hsl(191,100%,50%)] text-gray-100 font-fira"
                          />
                        </FormControl>
                        <FormMessage className="text-red-400 text-xs" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-fira text-sm font-bold text-gray-300 uppercase tracking-wider flex items-center space-x-2">
                          <Mail size={16} />
                          <span>Email</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="email"
                            placeholder="<EMAIL>"
                            className="bg-black/50 border-gray-600 focus:border-[hsl(191,100%,50%)] text-gray-100 font-fira"
                          />
                        </FormControl>
                        <FormMessage className="text-red-400 text-xs" />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="subject"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-fira text-sm font-bold text-gray-300 uppercase tracking-wider flex items-center space-x-2">
                        <FileText size={16} />
                        <span>Subject</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="What's this about?"
                          className="bg-black/50 border-gray-600 focus:border-[hsl(191,100%,50%)] text-gray-100 font-fira"
                        />
                      </FormControl>
                      <FormMessage className="text-red-400 text-xs" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-fira text-sm font-bold text-gray-300 uppercase tracking-wider flex items-center space-x-2">
                        <MessageCircle size={16} />
                        <span>Message</span>
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Tell me about your project or opportunity..."
                          rows={6}
                          className="bg-black/50 border-gray-600 focus:border-[hsl(191,100%,50%)] text-gray-100 font-fira resize-none"
                        />
                      </FormControl>
                      <FormMessage className="text-red-400 text-xs" />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full cyber-button py-4 rounded-lg font-fira font-bold text-lg group"
                >
                  {isSubmitting ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      <span>SENDING...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Send className="group-hover:translate-x-1 transition-transform" size={20} />
                      <span>SEND MESSAGE</span>
                    </div>
                  )}
                </Button>
              </form>
            </Form>
          </div>
        </div>
      </div>
    </section>
  );
}