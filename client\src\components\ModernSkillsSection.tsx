import { Code, Database, Globe, Cpu, Brain, Zap, Star, TrendingUp } from 'lucide-react';

const technicalSkills = [
  { name: "React.js", level: 90, icon: Globe, category: "Frontend", color: "text-blue-400" },
  { name: "JavaScript", level: 95, icon: Code, category: "Language", color: "text-yellow-400" },
  { name: "Python", level: 85, icon: Code, category: "Language", color: "text-green-400" },
  { name: "Node.js", level: 88, icon: Cpu, category: "Backend", color: "text-green-500" },
  { name: "MySQL", level: 82, icon: Database, category: "Database", color: "text-[hsl(261,100%,67%)]" },
  { name: "C++", level: 80, icon: Code, category: "Language", color: "text-[hsl(191,100%,50%)]" },
];

const frameworks = [
  "React.js", "Node.js", "Express.js", "Tailwind CSS", "Bootstrap", "TensorFlow", "Flask", "REST APIs"
];

const tools = [
  "VS Code", "Git/GitHub", "Postman", "Firebase", "Docker", "Vercel", "Netlify", "MySQL Workbench"
];

const specializations = [
  { 
    title: "Full-Stack Development", 
    description: "End-to-end web application development",
    icon: Globe,
    color: "text-[hsl(191,100%,50%)]",
    skills: ["React.js", "Node.js", "API Design", "Database Management"]
  },
  { 
    title: "Machine Learning", 
    description: "CNN models with 98% accuracy achievement",
    icon: Brain,
    color: "text-[hsl(261,100%,67%)]",
    skills: ["Python", "TensorFlow", "VGG16", "Data Processing"]
  },
  { 
    title: "System Architecture", 
    description: "Scalable platform design and implementation",
    icon: Cpu,
    color: "text-[hsl(213,100%,50%)]",
    skills: ["Microservices", "Database Design", "API Architecture", "Performance"]
  }
];

export default function ModernSkillsSection() {
  return (
    <section id="skills" className="py-12 sm:py-20 relative">
      <div className="container mx-auto px-4 sm:px-6">
        {/* Section Header */}
        <div className="text-center mb-8 sm:mb-16">
          <h2 className="font-orbitron text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black mb-4 sm:mb-6 tracking-wider">
            <span className="bg-gradient-to-r from-[#8B5FFF] via-[#00D9FF] to-[#FF6B9D] bg-clip-text text-transparent">
              TECH
            </span>
            <span className="text-white/90 ml-2">_STACK</span>
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-gray-300 max-w-2xl mx-auto">
            Modern technologies and frameworks I use to build exceptional software solutions
          </p>
        </div>

        {/* Specializations */}
        <div className="grid sm:grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-8 sm:mb-16">
          {specializations.map((spec, index) => {
            const IconComponent = spec.icon;
            return (
              <div key={spec.title} className="cyber-card p-4 sm:p-6 lg:p-8 rounded-2xl border border-[hsl(191,100%,50%)]/30 hover:border-[hsl(191,100%,50%)]/60 transition-all group">
                <div className="text-center space-y-6">
                  <div className={`w-20 h-20 mx-auto rounded-2xl bg-gradient-to-br from-black to-gray-900 border-2 border-${spec.color.split('-')[1]}-400/30 flex items-center justify-center group-hover:scale-110 transition-transform`}>
                    <IconComponent className={`${spec.color} group-hover:animate-pulse`} size={32} />
                  </div>
                  <div>
                    <h3 className={`font-orbitron text-xl font-bold ${spec.color} mb-3`}>
                      {spec.title}
                    </h3>
                    <p className="text-gray-400 mb-4 text-sm">
                      {spec.description}
                    </p>
                    <div className="flex flex-wrap gap-2 justify-center">
                      {spec.skills.map((skill) => (
                        <span 
                          key={skill}
                          className={`px-3 py-1 bg-black/50 border border-${spec.color.split('-')[1]}-400/30 rounded-full text-xs font-fira ${spec.color}`}
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Technical Skills Progress */}
        <div className="glass-effect p-4 sm:p-6 lg:p-8 rounded-2xl border border-[hsl(191,100%,50%)]/30 mb-8 sm:mb-16">
          <h3 className="font-orbitron text-lg sm:text-xl lg:text-2xl font-bold text-[hsl(191,100%,50%)] mb-4 sm:mb-6 lg:mb-8 text-center">
            CORE TECHNOLOGIES
          </h3>
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {technicalSkills.map((skill) => {
              const IconComponent = skill.icon;
              return (
                <div key={skill.name} className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <IconComponent className={skill.color} size={20} />
                      <span className="font-fira font-bold text-gray-100">{skill.name}</span>
                    </div>
                    <span className="font-fira text-sm text-gray-400">{skill.level}%</span>
                  </div>
                  <div className="relative">
                    <div className="w-full bg-gray-800 rounded-full h-2">
                      <div 
                        className="skill-bar h-2 rounded-full transition-all duration-1000 ease-out"
                        style={{ width: `${skill.level}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 font-fira">{skill.category}</div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Frameworks & Tools */}
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Frameworks */}
          <div className="cyber-card p-8 rounded-2xl border border-[hsl(261,100%,67%)]/30">
            <div className="flex items-center space-x-3 mb-6">
              <Zap className="text-[hsl(261,100%,67%)]" size={24} />
              <h3 className="font-orbitron text-xl font-bold text-[hsl(261,100%,67%)]">
                FRAMEWORKS & LIBRARIES
              </h3>
            </div>
            <div className="grid grid-cols-2 gap-3">
              {frameworks.map((framework, index) => (
                <div 
                  key={framework}
                  className="flex items-center space-x-2 p-3 bg-black/30 rounded-lg border border-[hsl(261,100%,67%)]/20 hover:border-[hsl(261,100%,67%)]/50 transition-all group"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <Star className="text-[hsl(261,100%,67%)] group-hover:animate-spin" size={14} />
                  <span className="font-fira text-sm text-gray-300">{framework}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Tools */}
          <div className="cyber-card p-8 rounded-2xl border border-[hsl(213,100%,50%)]/30">
            <div className="flex items-center space-x-3 mb-6">
              <TrendingUp className="text-[hsl(213,100%,50%)]" size={24} />
              <h3 className="font-orbitron text-xl font-bold text-[hsl(213,100%,50%)]">
                TOOLS & PLATFORMS
              </h3>
            </div>
            <div className="grid grid-cols-2 gap-3">
              {tools.map((tool, index) => (
                <div 
                  key={tool}
                  className="flex items-center space-x-2 p-3 bg-black/30 rounded-lg border border-[hsl(213,100%,50%)]/20 hover:border-[hsl(213,100%,50%)]/50 transition-all group"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <Star className="text-[hsl(213,100%,50%)] group-hover:animate-spin" size={14} />
                  <span className="font-fira text-sm text-gray-300">{tool}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}