# Portfolio Website

## Overview

This is a modern cyberpunk-themed portfolio website built as a full-stack web application. It features a futuristic design with neon accents, interactive 3D elements, and a comprehensive contact system. The application showcases personal information, skills, projects, and provides a way for visitors to get in touch.

## User Preferences

Preferred communication style: Simple, everyday language.

## Recent Changes

### Complete Website Redesign (January 27, 2025)
- Implemented modern cyberpunk-themed portfolio with futuristic design
- Created enhanced 3D particle background with 120 floating particles and geometric shapes
- Built modern component variants: ModernHeroSection, ModernNavigation, ModernAboutSection, ModernSkillsSection, ModernProjectsSection, ModernContactSection
- Removed horizontal energy lines from background per user preference
- Removed section divider lines between components for cleaner flow
- Redesigned footer to card-style with glass effect container (user approved final design)
- Added advanced CSS animations including 3D floating effects, spin-slow, and bounce-slow
- Integrated working contact form with proper validation and error handling

## System Architecture

The application follows a monolithic full-stack architecture with clear separation between frontend and backend concerns:

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter for client-side routing
- **Styling**: Tailwind CSS with custom cyberpunk theme
- **UI Components**: Radix UI primitives with shadcn/ui component library
- **3D Graphics**: Three.js with React Three Fiber for particle animations
- **State Management**: React Query (TanStack Query) for server state
- **Build Tool**: Vite for development and bundling

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **Data Storage**: In-memory storage with interface for database abstraction
- **Schema Validation**: Zod for runtime type checking
- **Database ORM**: Drizzle ORM configured for PostgreSQL (ready for integration)

## Key Components

### Frontend Components
1. **Layout Components**
   - Navigation with smooth scrolling
   - Loading screen with animated transitions
   - Three.js particle background

2. **Content Sections**
   - Hero section with animated typography
   - About section with personal information
   - Skills matrix with technical and soft skills
   - Projects showcase with filtering
   - Contact form with validation

3. **UI System**
   - Complete shadcn/ui component library
   - Custom cyberpunk theme with CSS variables
   - Responsive design for mobile and desktop

### Backend Components
1. **API Routes**
   - POST `/api/contact` - Submit contact form
   - GET `/api/contacts` - Retrieve all contacts (admin)

2. **Storage Layer**
   - Interface-based storage abstraction (`IStorage`)
   - In-memory implementation (`MemStorage`)
   - Database models defined with Drizzle schema

3. **Middleware**
   - Request logging
   - JSON parsing
   - Error handling

## Data Flow

1. **Contact Form Submission**
   - User fills out contact form on frontend
   - Form data validated with Zod schema
   - Data sent to backend via React Query mutation
   - Backend validates and stores contact information
   - Success/error feedback displayed to user

2. **Static Content**
   - Personal information, skills, and projects stored as static data
   - Rendered client-side with smooth animations
   - Three.js particles rendered continuously in background

## External Dependencies

### Core Framework Dependencies
- **React Ecosystem**: React, React DOM, React Query
- **UI/Styling**: Radix UI components, Tailwind CSS, class-variance-authority
- **3D Graphics**: Three.js, React Three Fiber, drei
- **Backend**: Express.js, Drizzle ORM, Zod validation

### Development Dependencies
- **Build Tools**: Vite, TypeScript, PostCSS
- **Database**: Neon serverless PostgreSQL adapter (configured but not used)
- **Development**: tsx for TypeScript execution, various Replit plugins

### Database Integration
The application is configured to use PostgreSQL with Drizzle ORM but currently uses in-memory storage. The database schema is defined and migration configuration is ready for deployment.

## Deployment Strategy

### Development
- Frontend served by Vite dev server with HMR
- Backend runs with tsx in watch mode
- Environment configured for Replit development

### Production Build
- Frontend built to static assets with Vite
- Backend bundled with esbuild for Node.js
- Single server serves both API and static files
- Database migrations managed with Drizzle Kit

### Environment Configuration
- Development and production modes supported
- Database URL configuration ready for PostgreSQL
- Replit-specific integrations for development environment

The application is designed to be easily deployable on platforms like Replit, Vercel, or traditional hosting providers with minimal configuration changes.