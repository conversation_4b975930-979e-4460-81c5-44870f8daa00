import { useEffect, useState } from "react";

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  speed: number;
  color: string;
  opacity: number;
}

export default function ThreeBackground() {
  const [particles, setParticles] = useState<Particle[]>([]);

  useEffect(() => {
    const generateParticles = () => {
      const newParticles: Particle[] = [];
      for (let i = 0; i < 120; i++) {
        const colors = ['#00D9FF', '#8B5FFF', '#00FF9F', '#FF6B9D', '#FFD700'];
        newParticles.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: Math.random() * 8 + 2,
          speed: Math.random() * 4 + 1,
          color: colors[Math.floor(Math.random() * colors.length)],
          opacity: Math.random() * 0.9 + 0.1
        });
      }
      setParticles(newParticles);
    };

    generateParticles();
  }, []);

  return (
    <div className="fixed inset-0 z-[-1] pointer-events-none overflow-hidden">
      {/* Deep space background */}
      <div className="absolute inset-0 bg-black">
        <div className="absolute inset-0 bg-gradient-radial from-[#0a0a0a] via-[#1a1a2e] to-[#16213e] animate-pulse" style={{ animationDuration: '12s' }} />
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-[#0D1B2A]/20 to-[#1B263B]/30" />
      </div>
      
      {/* Floating particles */}
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="absolute rounded-full animate-float-3d"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: particle.color,
            opacity: particle.opacity,
            boxShadow: `
              0 0 ${particle.size * 4}px ${particle.color},
              0 0 ${particle.size * 8}px ${particle.color}40,
              inset 0 0 ${particle.size}px ${particle.color}80
            `,
            transform: `perspective(1000px) rotateX(${particle.id * 15}deg) rotateY(${particle.id * 10}deg)`,
            animationDelay: `${particle.id * 0.1}s`,
            animationDuration: `${6 + particle.speed}s`
          }}
        />
      ))}
      
      {/* Cyberpunk grid overlay */}
      <div 
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage: `
            linear-gradient(rgba(0, 217, 255, 0.5) 2px, transparent 2px),
            linear-gradient(90deg, rgba(0, 217, 255, 0.5) 2px, transparent 2px),
            linear-gradient(rgba(139, 95, 255, 0.3) 1px, transparent 1px),
            linear-gradient(90deg, rgba(139, 95, 255, 0.3) 1px, transparent 1px)
          `,
          backgroundSize: '100px 100px, 100px 100px, 20px 20px, 20px 20px'
        }}
      />
      

      
      {/* Floating geometric shapes */}
      <div className="absolute top-20 left-20 w-16 h-16 border-2 border-[#00D9FF] opacity-20 animate-spin-slow"></div>
      <div className="absolute top-40 right-32 w-12 h-12 border-2 border-[#8B5FFF] opacity-15 animate-pulse transform rotate-45"></div>
      <div className="absolute bottom-32 left-40 w-20 h-20 border border-[#00FF9F] opacity-10 animate-bounce-slow rounded-full"></div>
    </div>
  );
}
